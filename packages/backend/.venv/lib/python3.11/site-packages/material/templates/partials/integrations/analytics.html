{#-
  This file was automatically generated - do not edit
-#}
{% if config.extra.analytics %}
  {% set provider = config.extra.analytics.provider %}
{% endif %}
{% if provider %}
  {% include "partials/integrations/analytics/" ~ provider ~ ".html" %}
  {% if config.extra.consent %}
    <script>if("undefined"!=typeof __md_analytics){var consent=__md_get("__consent");consent&&consent.analytics&&__md_analytics()}</script>
  {% else %}
    <script>"undefined"!=typeof __md_analytics&&__md_analytics()</script>
  {% endif %}
{% endif %}
