{#-
  This file was automatically generated - do not edit
-#}
<footer class="md-footer">
  {% if "navigation.footer" in features %}
    {% if page.previous_page or page.next_page %}
      {% if page.meta and page.meta.hide %}
        {% set hidden = "hidden" if "footer" in page.meta.hide %}
      {% endif %}
      <nav class="md-footer__inner md-grid" aria-label="{{ lang.t('footer') }}" {{ hidden }}>
        {% if page.previous_page %}
          {% set direction = lang.t("footer.previous") %}
          <a href="{{ page.previous_page.url | url }}" class="md-footer__link md-footer__link--prev" aria-label="{{ direction }}: {{ page.previous_page.title | e }}">
            <div class="md-footer__button md-icon">
              {% set icon = config.theme.icon.previous or "material/arrow-left" %}
              {% include ".icons/" ~ icon ~ ".svg" %}
            </div>
            <div class="md-footer__title">
              <span class="md-footer__direction">
                {{ direction }}
              </span>
              <div class="md-ellipsis">
                {{ page.previous_page.title }}
              </div>
            </div>
          </a>
        {% endif %}
        {% if page.next_page %}
          {% set direction = lang.t("footer.next") %}
          <a href="{{ page.next_page.url | url }}" class="md-footer__link md-footer__link--next" aria-label="{{ direction }}: {{ page.next_page.title | e }}">
            <div class="md-footer__title">
              <span class="md-footer__direction">
                {{ direction }}
              </span>
              <div class="md-ellipsis">
                {{ page.next_page.title }}
              </div>
            </div>
            <div class="md-footer__button md-icon">
              {% set icon = config.theme.icon.next or "material/arrow-right" %}
              {% include ".icons/" ~ icon ~ ".svg" %}
            </div>
          </a>
        {% endif %}
      </nav>
    {% endif %}
  {% endif %}
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      {% include "partials/copyright.html" %}
      {% if config.extra.social %}
        {% include "partials/social.html" %}
      {% endif %}
    </div>
  </div>
</footer>
