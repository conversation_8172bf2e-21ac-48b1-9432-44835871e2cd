{#-
  This file was automatically generated - do not edit
-#}
<div class="md-header__option">
  <div class="md-select">
    {% set icon = config.theme.icon.alternate or "material/translate" %}
    <button class="md-header__button md-icon" aria-label="{{ lang.t('select.language') }}">
      {% include ".icons/" ~ icon ~ ".svg" %}
    </button>
    <div class="md-select__inner">
      <ul class="md-select__list">
        {% for alt in config.extra.alternate %}
          <li class="md-select__item">
            <a href="{{ alt.link | url }}" hreflang="{{ alt.lang }}" class="md-select__link">
              {{ alt.name }}
            </a>
          </li>
        {% endfor %}
      </ul>
    </div>
  </div>
</div>
