{#-
  This file was automatically generated - do not edit
-#}
{% macro render_content(nav_item, ref = nav_item) %}
  {% if nav_item == ref or "navigation.indexes" in features %}
    {% if nav_item.is_index and nav_item.meta.icon %}
      {% include ".icons/" ~ nav_item.meta.icon ~ ".svg" %}
    {% endif %}
  {% endif %}
  {{ ref.title }}
{% endmacro %}
{% macro render(nav_item, ref = nav_item) %}
  {% set class = "md-tabs__item" %}
  {% if ref.active %}
    {% set class = class ~ " md-tabs__item--active" %}
  {% endif %}
  {% if nav_item.children %}
    {% set first = nav_item.children | first %}
    {% if first.children %}
      {{ render(first, ref) }}
    {% else %}
      <li class="{{ class }}">
        <a href="{{ first.url | url }}" class="md-tabs__link">
          {{ render_content(first, ref) }}
        </a>
      </li>
    {% endif %}
  {% else %}
    <li class="{{ class }}">
      <a href="{{ nav_item.url | url }}" class="md-tabs__link">
        {{ render_content(nav_item) }}
      </a>
    </li>
  {% endif %}
{% endmacro %}
