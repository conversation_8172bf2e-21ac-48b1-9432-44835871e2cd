{#-
  This file was automatically generated - do not edit
-#}
{% if page.meta and page.meta.hide %}
  {% set hidden = "hidden" if "tags" in page.meta.hide %}
{% endif %}
<nav class="md-tags" {{ hidden }}>
  {% for tag in tags %}
    {% set icon = "" %}
    {% if config.extra.tags %}
      {% set icon = " md-tag-icon" %}
      {% if tag.type %}
        {% set icon = icon ~ " md-tag--" ~ tag.type %}
      {% endif %}
    {% endif %}
    {% if tag.url %}
      <a href="{{ tag.url | url }}" class="md-tag{{ icon }}">
        {{- tag.name -}}
      </a>
    {% else %}
      <span class="md-tag{{ icon }}">
        {{- tag.name -}}
      </span>
    {% endif %}
  {% endfor %}
</nav>
