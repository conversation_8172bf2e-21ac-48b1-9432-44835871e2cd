{#-
  This file was automatically generated - do not edit
-#}
{% set cookies = config.extra.consent.cookies | d({}) %}
{% if config.extra.analytics %}
  {% if "analytics" not in cookies %}
    {% set _ = cookies.update({ "analytics": "Google Analytics" }) %}
  {% endif %}
{% endif %}
{% if config.repo_url and "github.com" in config.repo_url %}
  {% if "github" not in cookies %}
    {% set _ = cookies.update({ "github": "GitHub" }) %}
  {% endif %}
{% endif %}
{% set actions = config.extra.consent.actions %}
{% if not actions %}
  {% set actions = ["accept", "manage"] %}
{% endif %}
{% if "manage" not in actions %}
  {% set checked = "checked" %}
{% endif %}
<h4>{{ config.extra.consent.title }}</h4>
<p>{{ config.extra.consent.description }}</p>
<input class="md-toggle" type="checkbox" id="__settings" {{ checked }}>
<div class="md-consent__settings">
  <ul class="task-list">
    {% for type in cookies %}
      {% set checked = "" %}
      {% if cookies[type] is string %}
        {% set name = cookies[type] %}
        {% set checked = "checked" %}
      {% else %}
        {% set name = cookies[type].name %}
        {% if cookies[type].checked %}
          {% set checked = "checked" %}
        {% endif %}
      {% endif %}
      <li class="task-list-item">
        <label class="task-list-control">
          <input type="checkbox" name="{{ type }}" {{ checked }}>
          <span class="task-list-indicator"></span>
          {{ name }}
        </label>
      </li>
    {% endfor %}
  </ul>
</div>
<div class="md-consent__controls">
  {% for action in actions %}
    {% if action == "accept" %}
      <button class="md-button md-button--primary">
        {{- lang.t("consent.accept") -}}
      </button>
    {% endif %}
    {% if action == "reject" %}
      <button type="reset" class="md-button md-button--primary">
        {{- lang.t("consent.reject") -}}
      </button>
    {% endif %}
    {% if action == "manage" %}
      <label class="md-button" for="__settings">
        {{- lang.t("consent.manage") -}}
      </label>
    {% endif %}
  {% endfor %}
</div>
