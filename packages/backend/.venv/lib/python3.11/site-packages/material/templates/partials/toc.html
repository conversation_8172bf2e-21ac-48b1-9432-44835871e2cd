{#-
  This file was automatically generated - do not edit
-#}
{% set title = lang.t("toc") %}
{% if config.mdx_configs.toc and config.mdx_configs.toc.title %}
  {% set title = config.mdx_configs.toc.title %}
{% endif %}
<nav class="md-nav md-nav--secondary" aria-label="{{ title }}">
  {% set toc = page.toc %}
  {% set first = toc | first %}
  {% if first and first.level == 1 %}
    {% set toc = first.children %}
  {% endif %}
  {% if toc %}
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      {{ title }}
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      {% for toc_item in toc %}
        {% include "partials/toc-item.html" %}
      {% endfor %}
    </ul>
  {% endif %}
</nav>
