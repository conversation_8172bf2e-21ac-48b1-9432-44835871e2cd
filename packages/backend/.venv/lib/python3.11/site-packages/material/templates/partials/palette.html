{#-
  This file was automatically generated - do not edit
-#}
<form class="md-header__option" data-md-component="palette">
  {% for option in config.theme.palette %}
    {% set scheme  = option.scheme  | d("default", true) %}
    {% set primary = option.primary | d("indigo", true) %}
    {% set accent  = option.accent  | d("indigo", true) %}
    <input class="md-option" data-md-color-media="{{ option.media }}" data-md-color-scheme="{{ scheme | replace(' ', '-') }}" data-md-color-primary="{{ primary | replace(' ', '-') }}" data-md-color-accent="{{ accent | replace(' ', '-') }}" {% if option.toggle %} aria-label="{{ option.toggle.name }}" {% else %} aria-hidden="true" {% endif %} type="radio" name="__palette" id="__palette_{{ loop.index0 }}">
    {% if option.toggle %}
      <label class="md-header__button md-icon" title="{{ option.toggle.name }}" for="__palette_{{ loop.index % loop.length }}" hidden>
        {% include ".icons/" ~ option.toggle.icon ~ ".svg" %}
      </label>
    {% endif %}
  {% endfor %}
</form>
