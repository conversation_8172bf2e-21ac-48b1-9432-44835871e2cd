Metadata-Version: 2.3
Name: mkdocs-material
Version: 9.5.47
Summary: Documentation that simply works
Project-URL: Documentation, https://squidfunk.github.io/mkdocs-material/
Project-URL: Source, https://github.com/squidfunk/mkdocs-material
Project-URL: Changelog, https://squidfunk.github.io/mkdocs-material/changelog/
Project-URL: Issues, https://github.com/squidfunk/mkdocs-material/issues
Project-URL: Funding, https://github.com/sponsors/squidfunk
Author-email: <PERSON> <<EMAIL>>
License: MIT
Keywords: documentation,mkdocs,theme
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Web Environment
Classifier: Framework :: MkDocs
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: JavaScript
Classifier: Programming Language :: Python
Classifier: Topic :: Documentation
Classifier: Topic :: Software Development :: Documentation
Classifier: Topic :: Text Processing :: Markup :: HTML
Requires-Python: >=3.8
Requires-Dist: babel~=2.10
Requires-Dist: colorama~=0.4
Requires-Dist: jinja2~=3.0
Requires-Dist: markdown~=3.2
Requires-Dist: mkdocs-material-extensions~=1.3
Requires-Dist: mkdocs~=1.6
Requires-Dist: paginate~=0.5
Requires-Dist: pygments~=2.16
Requires-Dist: pymdown-extensions~=10.2
Requires-Dist: regex>=2022.4
Requires-Dist: requests~=2.26
Provides-Extra: git
Requires-Dist: mkdocs-git-committers-plugin-2~=1.1; extra == 'git'
Requires-Dist: mkdocs-git-revision-date-localized-plugin>=1.2.4,~=1.2; extra == 'git'
Provides-Extra: imaging
Requires-Dist: cairosvg~=2.6; extra == 'imaging'
Requires-Dist: pillow~=10.2; extra == 'imaging'
Provides-Extra: recommended
Requires-Dist: mkdocs-minify-plugin~=0.7; extra == 'recommended'
Requires-Dist: mkdocs-redirects~=1.2; extra == 'recommended'
Requires-Dist: mkdocs-rss-plugin~=1.6; extra == 'recommended'
Description-Content-Type: text/markdown

<p align="center">
  <a href="https://squidfunk.github.io/mkdocs-material/">
    <img src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/logo.svg" width="320" alt="Material for MkDocs">
  </a>
</p>

<p align="center">
  <strong>
    A powerful documentation framework on top of
    <a href="https://www.mkdocs.org/">MkDocs</a>
  </strong>
</p>

<p align="center">
  <a href="https://github.com/squidfunk/mkdocs-material/actions"><img
    src="https://github.com/squidfunk/mkdocs-material/workflows/build/badge.svg?branch=master"
    alt="Build"
  /></a>
  <a href="https://pypistats.org/packages/mkdocs-material"><img
    src="https://img.shields.io/pypi/dm/mkdocs-material.svg"
    alt="Downloads"
  /></a>
  <a href="https://pypi.org/project/mkdocs-material"><img
    src="https://img.shields.io/pypi/v/mkdocs-material.svg"
    alt="Python Package Index"
  /></a>
  <a href="https://hub.docker.com/r/squidfunk/mkdocs-material/"><img
    src="https://img.shields.io/docker/pulls/squidfunk/mkdocs-material"
    alt="Docker Pulls"
  /></a>
  <a href="https://github.com/sponsors/squidfunk"><img
    src="https://img.shields.io/github/sponsors/squidfunk"
    alt="Sponsors"
  /></a>
</p>

<p align="center">
  Write your documentation in Markdown and create a professional static site for
  your Open Source or commercial project in minutes – searchable, customizable,
  more than 60 languages, for all devices.
</p>

<p align="center">
  <a href="https://squidfunk.github.io/mkdocs-material/getting-started/">
    <img src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/screenshot.png" width="700" />
  </a>
</p>

<p align="center">
  <em>
    Check out the demo –
    <a
      href="https://squidfunk.github.io/mkdocs-material/"
    >squidfunk.github.io/mkdocs-material</a>.
  </em>
</p>

<h2></h2>
<p id="premium-sponsors">&nbsp;</p>
<p align="center"><strong>Silver sponsors</strong></p>
<p align="center">
  <a href="https://fastapi.tiangolo.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-fastapi.png" height="120"
  /></a>
  <a href="https://www.trendpop.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-trendpop.png" height="120"
  /></a>
  <a href="https://documentation.sailpoint.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-sailpoint.png" height="120"
  /></a>
  <a href="https://futureplc.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-future.svg" height="120"
  /></a>
  <a href="https://opensource.siemens.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-siemens.png" height="120"
  /></a>
</p>
<p>&nbsp;</p>
<p align="center"><strong>Bronze sponsors</strong></p>
<p align="center">
  <a href="https://cirrus-ci.org/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-cirrus-ci.png" height="58"
  /></a>
  <a href="https://docs.baslerweb.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-basler.png" height="58"
  /></a>
  <a href="https://kx.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-kx.png" height="58"
  /></a>
  <a href="https://orion-docs.prefect.io/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-prefect.png" height="58"
  /></a>
  <a href="https://www.zenoss.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-zenoss.png" height="58"
  /></a>
  <a href="https://docs.posit.co" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-posit.png" height="58"
  /></a>
  <a href="https://n8n.io" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-n8n.png" height="58"
  /></a>
  <a href="https://www.dogado.de" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-dogado.png" height="58"
  /></a>
  <a href="https://wwt.com" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-wwt.png" height="58"
  /></a>
  <a href="https://coda.io" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-coda.png" height="58"
  /></a>
  <a href="https://elastic.co" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-elastic.png" height="58"
  /></a>
  <a href="https://ipfabric.io/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-ip-fabric.png" height="58"
  /></a>
  <a href="https://www.apex.ai/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-apex-ai.png" height="58"
  /></a>
  <a href="https://jitterbit.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-jitterbit.png" height="58"
  /></a>
  <a href="https://sparkfun.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-sparkfun.png" height="58"
  /></a>
  <a href="https://eccenca.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-eccenca.png" height="58"
  /></a>
  <a href="https://neptune.ai/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-neptune-ai.png" height="58"
  /></a>
  <!-- <a href="https://cash.app/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-cashapp.png" height="58"
  /></a> -->
  <a href="https://rackn.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-rackn.png" height="58"
  /></a>
  <a href="https://civicactions.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-civic-actions.png" height="58"
  /></a>
  <a href="https://bitcrowd.net/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-bitcrowd.png" height="58"
  /></a>
  <a href="https://getscreen.me/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-getscreenme.png" height="58"
  /></a>
  <a href="https://botcity.dev/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-botcity.png" height="58"
  /></a>
  <a href="https://www.springernature.com/gp" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-sn-technology.png" height="58"
  /></a>
  <a href="https://kolena.io/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-kolena.png" height="58"
  /></a>
  <a href="https://www.evergiving.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-evergiving.png" height="58"
  /></a>
  <a href="https://koor.tech/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-koor.png" height="58"
  /></a>
  <a href="https://astral.sh/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-astral.png" height="58"
  /></a>
  <a href="https://oikolab.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-oikolab.png" height="58"
  /></a>
  <a href="https://www.buhlergroup.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-buhler.png" height="58"
  /></a>
  <a href="https://3dr.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-3dr.png" height="58"
  /></a>
  <a href="https://spotware.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-spotware.png" height="58"
  /></a>
  <a href="https://milfordasset.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-milford.png" height="58"
  /></a>
  <a href="https://www.lechler.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-lechler.png" height="58"
  /></a>
  <a href="https://invers.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-invers.png" height="58"
  /></a>
  <a href="https://maxar.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-maxar.png" height="58"
  /></a>
  <a href="https://www.equipmentshare.com/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-equipmentshare.png" height="58"
  /></a>
  <a href="https://hummingbot.org/" target=_blank><img
    src="https://raw.githubusercontent.com/squidfunk/mkdocs-material/master/.github/assets/sponsors/sponsor-hummingbot.png" height="58"
  /></a>
</p>
<p>&nbsp;</p>

## Everything you would expect

### It's just Markdown

Focus on the content of your documentation and create a professional static site
in minutes. No need to know HTML, CSS or JavaScript – let Material for MkDocs do
the heavy lifting for you.

### Works on all devices

Serve your documentation with confidence – Material for MkDocs automatically
adapts to perfectly fit the available screen estate, no matter the type or size
of the viewing device. Desktop. Tablet. Mobile. All great.

### Made to measure

Make it yours – change the colors, fonts, language, icons, logo, and more with
a few lines of configuration. Material for MkDocs can be easily extended and
provides many options to alter appearance and behavior.

### Fast and lightweight

Don't let your users wait – get incredible value with a small footprint by using
one of the fastest themes available with excellent performance, yielding optimal
search engine rankings and happy users that return.

### Built for everyone

Make accessibility a priority – users can navigate your documentation with touch
devices, keyboards, and screen readers. Semantic markup ensures that your
documentation works for everyone.

### Open Source

Trust 20,000+ users – choose a mature and actively maintained solution built
with state-of-the-art Open Source technologies. Keep ownership of your content
without fear of vendor lock-in. Licensed under MIT.

## Quick start

Material for MkDocs can be installed with `pip`:

``` sh
pip install mkdocs-material
```

Add the following lines to `mkdocs.yml`:

``` yaml
theme:
  name: material
```

For detailed installation instructions, configuration options, and a demo, visit
[squidfunk.github.io/mkdocs-material][Material for MkDocs]

  [Material for MkDocs]: https://squidfunk.github.io/mkdocs-material/

## Trusted by ...

### ... industry leaders

[ArXiv](https://info.arxiv.org),
[Atlassian](https://atlassian.github.io/data-center-helm-charts/),
[AWS](https://aws.github.io/copilot-cli/),
[Bloomberg](https://bloomberg.github.io/selekt/),
[CERN](http://abpcomputing.web.cern.ch/),
[CloudFlare](https://cloudflare.github.io/itty-router-openapi/),
[Datadog](https://datadoghq.dev/integrations-core/),
[Google](https://google.github.io/accompanist/),
[Harvard](https://informatics.fas.harvard.edu/),
[Hewlett Packard](https://hewlettpackard.github.io/squest/),
[HSBC](https://hsbc.github.io/pyratings/),
[ING](https://ing-bank.github.io/baker/),
[Intel](https://open-amt-cloud-toolkit.github.io/docs/),
[JetBrains](https://jetbrains.github.io/projector-client/mkdocs/),
[LinkedIn](https://linkedin.github.io/school-of-sre/),
[Microsoft](https://microsoft.github.io/code-with-engineering-playbook/),
[Mozilla](https://mozillafoundation.github.io/engineering-handbook/),
[Netflix](https://netflix.github.io/titus/),
[Red Hat](https://ansible.readthedocs.io/projects/lint/),
[Roboflow](https://inference.roboflow.com/),
[Salesforce](https://policy-sentry.readthedocs.io/),
[SIEMENS](https://opensource.siemens.com/),
[Slack](https://slackhq.github.io/circuit/),
[Square](https://square.github.io/okhttp/),
[Uber](https://uber-go.github.io/fx/),
[Zalando](https://opensource.zalando.com/skipper/)

### ... and successful Open Source projects

[Amp](https://amp.rs/docs/),
[Arduino](https://arduino.github.io/arduino-cli/),
[Auto-GPT](https://docs.agpt.co/),
[AutoKeras](https://autokeras.com/),
[BFE](https://www.bfe-networks.net/),
[CentOS](https://docs.infra.centos.org/),
[Crystal](https://crystal-lang.org/reference/),
[eBPF](https://ebpf-go.dev/),
[Electron](https://www.electron.build/),
[FastAPI](https://fastapi.tiangolo.com/),
[Freqtrade](https://www.freqtrade.io/en/stable/),
[GoReleaser](https://goreleaser.com/),
[HedgeDoc](https://docs.hedgedoc.org/),
[Hummingbot](https://hummingbot.org/),
[Knative](https://knative.dev/docs/),
[Kubernetes](https://kops.sigs.k8s.io/),
[kSQL](https://docs.ksqldb.io/),
[NetBox](https://netboxlabs.com/docs/netbox/en/stable/),
[Nokogiri](https://nokogiri.org/),
[OpenFaaS](https://docs.openfaas.com/),
[OpenSSL](https://docs.openssl.org/),
[Orchard Core](https://docs.orchardcore.net/en/latest/),
[Percona](https://docs.percona.com/percona-monitoring-and-management/),
[Pi-Hole](https://docs.pi-hole.net/),
[Pydantic](https://pydantic-docs.helpmanual.io/),
[PyPI](https://docs.pypi.org/),
[Renovate](https://docs.renovatebot.com/),
[RetroPie](https://retropie.org.uk/docs/),
[Supervision](https://supervision.roboflow.com/latest/),
[Traefik](https://docs.traefik.io/),
[Trivy](https://aquasecurity.github.io/trivy/),
[Typer](https://typer.tiangolo.com/),
[tinygrad](https://docs.tinygrad.org/),
[Ultralytics](https://docs.ultralytics.com/),
[Vapor](https://docs.vapor.codes/),
[WebKit](https://docs.webkit.org/),
[WTF](https://wtfutil.com/),
[ZeroNet](https://zeronet.io/docs/)

## License

**MIT License**

Copyright (c) 2016-2024 Martin Donath

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to
deal in the Software without restriction, including without limitation the
rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
sell copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
IN THE SOFTWARE.
