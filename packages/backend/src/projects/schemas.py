"""
Pydantic schemas for project management.
"""
from typing import List, Optional

from pydantic import Field, field_validator

from src.constants import ProjectStatus, TextRegionType, OCRStatus, TranslationStatus, SUPPORTED_LANGUAGES
from src.schemas import BaseSchema, CustomModel


# Project schemas
class ProjectCreate(CustomModel):
    """Schema for creating a new project."""

    name: str = Field(..., min_length=1, max_length=255,
                      description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    source_language: str = Field(
        default="japanese", description="Source language")
    target_language: str = Field(
        default="english", description="Target language")

    @field_validator("source_language", "target_language")
    @classmethod
    def validate_language(cls, v):
        if v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {v}")
        return v


class ProjectUpdate(CustomModel):
    """Schema for updating a project."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    status: Optional[ProjectStatus] = Field(None, description="Project status")
    source_language: Optional[str] = Field(None, description="Source language")
    target_language: Optional[str] = Field(None, description="Target language")

    @field_validator("source_language", "target_language")
    @classmethod
    def validate_language(cls, v):
        if v is not None and v not in SUPPORTED_LANGUAGES:
            raise ValueError(f"Unsupported language: {v}")
        return v


class ProjectResponse(BaseSchema):
    """Schema for project response."""

    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    status: ProjectStatus = Field(..., description="Project status")
    source_language: str = Field(..., description="Source language")
    target_language: str = Field(..., description="Target language")
    page_count: int = Field(
        default=0, description="Number of pages in project")


# Project Page schemas
class ProjectPageCreate(CustomModel):
    """Schema for creating a new project page."""

    page_number: int = Field(..., ge=1, description="Page number")
    original_filename: str = Field(..., description="Original filename")


class ProjectPageResponse(BaseSchema):
    """Schema for project page response."""

    project_id: str = Field(..., description="Project ID")
    page_number: int = Field(..., description="Page number")
    original_filename: str = Field(..., description="Original filename")
    file_path: str = Field(..., description="File path")
    file_size: int = Field(..., description="File size in bytes")
    image_width: Optional[int] = Field(
        None, description="Image width in pixels")
    image_height: Optional[int] = Field(
        None, description="Image height in pixels")
    ocr_status: OCRStatus = Field(..., description="OCR processing status")
    text_region_count: int = Field(
        default=0, description="Number of text regions")


# Text Region schemas
class TextRegionCreate(CustomModel):
    """Schema for creating a new text region."""

    region_type: TextRegionType = Field(
        default=TextRegionType.SPEECH_BUBBLE, description="Type of text region")
    x: float = Field(..., ge=0, le=1, description="X coordinate (normalized)")
    y: float = Field(..., ge=0, le=1, description="Y coordinate (normalized)")
    width: float = Field(..., gt=0, le=1, description="Width (normalized)")
    height: float = Field(..., gt=0, le=1, description="Height (normalized)")
    original_text: Optional[str] = Field(
        None, description="Original text content")
    confidence_score: Optional[float] = Field(
        None, ge=0, le=1, description="OCR confidence score")


class TextRegionUpdate(CustomModel):
    """Schema for updating a text region."""

    region_type: Optional[TextRegionType] = Field(
        None, description="Type of text region")
    x: Optional[float] = Field(
        None, ge=0, le=1, description="X coordinate (normalized)")
    y: Optional[float] = Field(
        None, ge=0, le=1, description="Y coordinate (normalized)")
    width: Optional[float] = Field(
        None, gt=0, le=1, description="Width (normalized)")
    height: Optional[float] = Field(
        None, gt=0, le=1, description="Height (normalized)")
    original_text: Optional[str] = Field(
        None, description="Original text content")
    translated_text: Optional[str] = Field(
        None, description="Translated text content")
    translation_status: Optional[TranslationStatus] = Field(
        None, description="Translation status")
    font_family: Optional[str] = Field(
        None, max_length=100, description="Font family")
    font_size: Optional[int] = Field(
        None, ge=8, le=72, description="Font size")
    font_color: Optional[str] = Field(
        None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Font color (hex)")
    background_color: Optional[str] = Field(
        None, pattern=r"^#[0-9A-Fa-f]{6}$", description="Background color (hex)")


class TextRegionResponse(BaseSchema):
    """Schema for text region response."""

    page_id: str = Field(..., description="Page ID")
    region_type: TextRegionType = Field(..., description="Type of text region")
    x: float = Field(..., description="X coordinate (normalized)")
    y: float = Field(..., description="Y coordinate (normalized)")
    width: float = Field(..., description="Width (normalized)")
    height: float = Field(..., description="Height (normalized)")
    original_text: Optional[str] = Field(
        None, description="Original text content")
    confidence_score: Optional[float] = Field(
        None, description="OCR confidence score")
    translated_text: Optional[str] = Field(
        None, description="Translated text content")
    translation_status: TranslationStatus = Field(
        ..., description="Translation status")
    font_family: Optional[str] = Field(None, description="Font family")
    font_size: Optional[int] = Field(None, description="Font size")
    font_color: Optional[str] = Field(None, description="Font color (hex)")
    background_color: Optional[str] = Field(
        None, description="Background color (hex)")


# Detailed project response with pages and text regions
class ProjectDetailResponse(ProjectResponse):
    """Schema for detailed project response with pages."""

    pages: List[ProjectPageResponse] = Field(
        default=[], description="Project pages")


class ProjectPageDetailResponse(ProjectPageResponse):
    """Schema for detailed project page response with text regions."""

    text_regions: List[TextRegionResponse] = Field(
        default=[], description="Text regions")
