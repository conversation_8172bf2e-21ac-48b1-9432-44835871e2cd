"""
Database connection and configuration for SQLite.
"""
from typing import Async<PERSON>enerator

from databases import Database
from sqlalchemy import MetaD<PERSON>, create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from src.config import settings

# SQLite naming conventions for indexes
SQLITE_INDEXES_NAMING_CONVENTION = {
    "ix": "%(column_0_label)s_idx",
    "uq": "%(table_name)s_%(column_0_name)s_key",
    "ck": "%(table_name)s_%(constraint_name)s_check",
    "fk": "%(table_name)s_%(column_0_name)s_fkey",
    "pk": "%(table_name)s_pkey",
}

metadata = MetaData(naming_convention=SQLITE_INDEXES_NAMING_CONVENTION)

# Database instance for async operations (legacy - keeping for compatibility)
database = Database(settings.DATABASE_URL)

# SQLAlchemy engine for migrations and sync operations
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False}  # Required for SQLite
)

# Async SQLAlchemy engine
async_engine = create_async_engine(
    settings.DATABASE_URL.replace("sqlite://", "sqlite+aiosqlite://"),
    echo=False
)

# Session factory for sync operations
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Async session factory
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Base class for SQLAlchemy models
Base = declarative_base(metadata=metadata)


def get_db():
    """Dependency to get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_database():
    """Dependency to get async database connection."""
    return database


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get async SQLAlchemy session."""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
