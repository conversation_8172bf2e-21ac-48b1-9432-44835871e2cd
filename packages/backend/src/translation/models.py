"""
SQLAlchemy models for translation processing.
"""
from sqlalchemy import Column, String, Text, Float, Integer, ForeignKey, JSON
from sqlalchemy.orm import relationship

from src.constants import TranslationStatus, LLMProvider
from src.models import BaseModel


class TranslationJob(BaseModel):
    """Translation job model for tracking translation tasks."""

    __tablename__ = "translation_job"

    text_region_id = Column(String(36), ForeignKey(
        "text_region.id"), nullable=False)
    status = Column(
        String(50), default=TranslationStatus.PENDING, nullable=False)
    provider = Column(String(50), default=LLMProvider.CLAUDE, nullable=False)

    # Languages
    source_language = Column(String(50), nullable=False)
    target_language = Column(String(50), nullable=False)

    # Input and output
    original_text = Column(Text, nullable=False)
    translated_text = Column(Text, nullable=True)

    # Processing details
    prompt_used = Column(Text, nullable=True)
    raw_response = Column(Text, nullable=True)
    processing_time_seconds = Column(Float, nullable=True)

    # Quality metrics
    confidence_score = Column(Float, nullable=True)
    quality_score = Column(Float, nullable=True)

    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)

    # Additional metadata
    extra_data = Column(JSON, nullable=True)

    # Relationships
    text_region = relationship("TextRegion", foreign_keys=[text_region_id])
    alternatives = relationship(
        "TranslationAlternative", back_populates="job", cascade="all, delete-orphan")


class TranslationAlternative(BaseModel):
    """Translation alternative model for storing multiple translation options."""

    __tablename__ = "translation_alternative"

    job_id = Column(String(36), ForeignKey(
        "translation_job.id"), nullable=False)

    # Translation content
    translated_text = Column(Text, nullable=False)
    confidence_score = Column(Float, nullable=True)
    quality_score = Column(Float, nullable=True)

    # Ranking and selection
    rank = Column(Integer, nullable=False)
    # Using string for SQLite compatibility
    is_selected = Column(String(5), default="false", nullable=False)

    # Additional metadata
    extra_data = Column(JSON, nullable=True)

    # Relationships
    job = relationship("TranslationJob", back_populates="alternatives")


class TranslationTemplate(BaseModel):
    """Translation template model for storing reusable translation patterns."""

    __tablename__ = "translation_template"

    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)

    # Languages
    source_language = Column(String(50), nullable=False)
    target_language = Column(String(50), nullable=False)

    # Template content
    source_pattern = Column(Text, nullable=False)
    target_pattern = Column(Text, nullable=False)

    # Usage statistics
    usage_count = Column(Integer, default=0, nullable=False)

    # Categorization
    category = Column(String(100), nullable=True)
    tags = Column(JSON, nullable=True)

    # Quality metrics
    average_quality_score = Column(Float, nullable=True)
