"""
Business logic for OCR processing.
"""
from typing import List, Optional

from sqlalchemy import select, update, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.ocr.models import OCRJob, OCRResult
from src.ocr.schemas import (
    OCRJobCreate, OCRJobResponse, OCRJobDetailResponse,
    OCRResultCreate, OCRResultResponse,
    OCRProcessRequest, OCRBatchProcessRequest,
    OCRStatistics, LLMOCRResponse, LLMOCRRegion
)
from src.ocr.exceptions import OCRJobNotFound, OCRProcessingFailed
from src.constants import OCRStatus, LLMProvider, DEFAULT_OCR_PROMPT
from src.config import settings


class OCRService:
    """Service class for OCR processing operations."""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def create_ocr_job(self, job_data: OCRJobCreate) -> OCRJobResponse:
        """Create a new OCR job."""
        # Create ORM model instance - timestamps will be set automatically
        ocr_job = OCRJob(
            page_id=job_data.page_id,
            provider=job_data.provider,
            prompt_used=job_data.custom_prompt or DEFAULT_OCR_PROMPT,
            status=OCRStatus.PENDING
        )

        self.session.add(ocr_job)
        await self.session.commit()
        await self.session.refresh(ocr_job)

        return await self.get_ocr_job(ocr_job.id)

    async def get_ocr_job(self, job_id: str) -> OCRJobResponse:
        """Get an OCR job by ID."""
        query = select(OCRJob).where(OCRJob.id == job_id)
        result = await self.session.execute(query)
        row = result.first()

        if not row:
            raise OCRJobNotFound(f"OCR job with ID {job_id} not found")

        return OCRJobResponse(**dict(row._mapping))

    async def get_ocr_jobs_by_page(self, page_id: str) -> List[OCRJobResponse]:
        """Get all OCR jobs for a page."""
        query = select(OCRJob).where(OCRJob.page_id ==
                                     page_id).order_by(OCRJob.created_at.desc())
        result = await self.session.execute(query)
        rows = result.fetchall()
        return [OCRJobResponse(**dict(row._mapping)) for row in rows]

    async def get_ocr_job_detail(self, job_id: str) -> OCRJobDetailResponse:
        """Get detailed OCR job information with results."""
        job = await self.get_ocr_job(job_id)
        results = await self.get_ocr_results(job_id)

        return OCRJobDetailResponse(
            **job.model_dump(),
            results=results
        )

    async def get_ocr_results(self, job_id: str) -> List[OCRResultResponse]:
        """Get all OCR results for a job."""
        query = select(OCRResult).where(OCRResult.job_id ==
                                        job_id).order_by(OCRResult.created_at)
        result = await self.session.execute(query)
        rows = result.fetchall()
        return [OCRResultResponse(**dict(row._mapping)) for row in rows]

    async def update_ocr_job_status(
        self,
        job_id: str,
        status: OCRStatus,
        error_message: Optional[str] = None,
        processing_time: Optional[float] = None
    ) -> OCRJobResponse:
        """Update OCR job status."""
        update_data = {"status": status}

        if error_message:
            update_data["error_message"] = error_message

        if processing_time:
            update_data["processing_time_seconds"] = processing_time

        query = update(OCRJob).where(OCRJob.id == job_id).values(**update_data)
        await self.session.execute(query)
        await self.session.commit()

        return await self.get_ocr_job(job_id)

    async def save_ocr_results(self, job_id: str, results: List[OCRResultCreate]) -> List[OCRResultResponse]:
        """Save OCR results for a job."""
        saved_results = []

        for result_data in results:
            # Create ORM model instance - timestamps will be set automatically
            ocr_result = OCRResult(
                job_id=job_id,
                detected_text=result_data.detected_text,
                confidence_score=result_data.confidence_score,
                region_type=result_data.region_type,
                x=result_data.x,
                y=result_data.y,
                width=result_data.width,
                height=result_data.height,
                extra_data=result_data.metadata  # Note: renamed from metadata to extra_data
            )

            self.session.add(ocr_result)
            await self.session.commit()
            await self.session.refresh(ocr_result)

            # Convert to response schema
            saved_results.append(OCRResultResponse(
                id=ocr_result.id,
                job_id=ocr_result.job_id,
                detected_text=ocr_result.detected_text,
                confidence_score=ocr_result.confidence_score,
                region_type=ocr_result.region_type,
                x=ocr_result.x,
                y=ocr_result.y,
                width=ocr_result.width,
                height=ocr_result.height,
                metadata=ocr_result.extra_data,  # Map back to metadata for response
                created_at=ocr_result.created_at,
                updated_at=ocr_result.updated_at
            ))

        # Update job statistics
        await self._update_job_statistics(job_id)

        return saved_results

    async def _update_job_statistics(self, job_id: str):
        """Update job statistics after saving results."""
        # Count total regions and calculate average confidence
        stats_query = select(
            func.count(OCRResult.id).label("total_regions"),
            func.avg(OCRResult.confidence_score).label("avg_confidence")
        ).where(OCRResult.job_id == job_id)

        stats_result = await self.session.execute(stats_query)
        stats = stats_result.first()

        update_query = update(OCRJob).where(OCRJob.id == job_id).values(
            total_regions_detected=stats.total_regions or 0,
            average_confidence=stats.avg_confidence
        )

        await self.session.execute(update_query)
        await self.session.commit()

    async def process_ocr_request(self, request: OCRProcessRequest) -> OCRJobResponse:
        """Process an OCR request."""
        # Create OCR job
        job_data = OCRJobCreate(
            page_id=request.page_id,
            provider=request.provider or LLMProvider.CLAUDE,
            custom_prompt=request.custom_prompt
        )

        job = await self.create_ocr_job(job_data)

        # Start processing (this would be done asynchronously in a real implementation)
        # For now, we'll just mark it as processing
        await self.update_ocr_job_status(job.id, OCRStatus.PROCESSING)

        return job

    async def get_ocr_statistics(self, project_id: Optional[str] = None) -> OCRStatistics:
        """Get OCR processing statistics."""
        base_query = select(OCRJob)

        if project_id:
            # Join with ProjectPage to filter by project
            from src.projects.models import ProjectPage
            base_query = base_query.join(ProjectPage).where(
                ProjectPage.project_id == project_id)

        # Count jobs by status
        status_counts = {}
        for status in OCRStatus:
            count_query = base_query.where(OCRJob.status == status)
            count = await self.session.scalar(select(func.count()).select_from(count_query.subquery()))
            status_counts[status] = count or 0

        # Calculate averages
        avg_time_query = select(func.avg(OCRJob.processing_time_seconds)).select_from(
            base_query.subquery())
        avg_confidence_query = select(
            func.avg(OCRJob.average_confidence)).select_from(base_query.subquery())
        total_regions_query = select(
            func.sum(OCRJob.total_regions_detected)).select_from(base_query.subquery())

        avg_time = await self.session.scalar(avg_time_query)
        avg_confidence = await self.session.scalar(avg_confidence_query)
        total_regions = await self.session.scalar(total_regions_query)

        return OCRStatistics(
            total_jobs=sum(status_counts.values()),
            completed_jobs=status_counts.get(OCRStatus.COMPLETED, 0),
            failed_jobs=status_counts.get(OCRStatus.FAILED, 0),
            pending_jobs=status_counts.get(OCRStatus.PENDING, 0),
            processing_jobs=status_counts.get(OCRStatus.PROCESSING, 0),
            average_processing_time=avg_time,
            total_regions_detected=total_regions or 0,
            average_confidence=avg_confidence
        )
