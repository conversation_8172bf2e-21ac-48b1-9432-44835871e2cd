"""
Global constants for ho-trans application.
"""
from enum import Enum


class Environment(str, Enum):
    """Application environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    CLAUDE = "claude"
    OPENAI = "openai"
    GEMINI = "gemini"


class TranslationStatus(str, Enum):
    """Translation status types."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ProjectStatus(str, Enum):
    """Project status types."""
    DRAFT = "draft"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class TextRegionType(str, Enum):
    """Types of text regions in manga."""
    SPEECH_BUBBLE = "speech_bubble"
    THOUGHT_BUBBLE = "thought_bubble"
    NARRATION = "narration"
    SOUND_EFFECT = "sound_effect"
    SIGN = "sign"
    OTHER = "other"


class OCRStatus(str, Enum):
    """OCR processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


# Error codes
class ErrorCode:
    """Application error codes."""

    # General
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    NOT_FOUND = "NOT_FOUND"

    # Projects
    PROJECT_NOT_FOUND = "PROJECT_NOT_FOUND"
    PROJECT_ALREADY_EXISTS = "PROJECT_ALREADY_EXISTS"

    # OCR
    OCR_PROCESSING_FAILED = "OCR_PROCESSING_FAILED"
    UNSUPPORTED_IMAGE_FORMAT = "UNSUPPORTED_IMAGE_FORMAT"
    IMAGE_TOO_LARGE = "IMAGE_TOO_LARGE"

    # Translation
    TRANSLATION_FAILED = "TRANSLATION_FAILED"
    UNSUPPORTED_LANGUAGE = "UNSUPPORTED_LANGUAGE"

    # LLM Providers
    LLM_API_ERROR = "LLM_API_ERROR"
    LLM_RATE_LIMIT = "LLM_RATE_LIMIT"
    INVALID_API_KEY = "INVALID_API_KEY"


# Supported languages
SUPPORTED_LANGUAGES = {
    "japanese": "Japanese",
    "english": "English",
    "indonesian": "Indonesian",
    "korean": "Korean",
    "chinese": "Chinese"
}

# Default prompts for LLM providers
DEFAULT_OCR_PROMPT = """
You are an expert at reading text from manga/comic images. 
Analyze the provided image and extract all visible text.
For each text region, identify:
1. The text content (in original language)
2. The type of text (speech bubble, thought bubble, narration, sound effect, sign, other)
3. The approximate position/coordinates if possible

Return the results in a structured JSON format.
"""

DEFAULT_TRANSLATION_PROMPT = """
You are an expert translator specializing in manga/comic translation.
Translate the provided text from {source_language} to {target_language}.
Maintain the tone, style, and cultural context appropriate for manga.
Consider the text type (dialogue, narration, sound effects) when translating.

Original text: {text}
Text type: {text_type}

Provide only the translated text without additional explanations.
"""
